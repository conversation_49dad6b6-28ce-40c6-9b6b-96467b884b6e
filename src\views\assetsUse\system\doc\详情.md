

## 制度信息-详情查询


**接口地址**:`/jeecg-boot/biz/institution/detail`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|id|query|true|string||
|X-Access-Token|token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|接口返回对象«资产制度信息»|
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回代码|integer(int32)|integer(int32)|
|message|返回处理消息|string||
|result|返回数据对象|资产制度信息|资产制度信息|
|&emsp;&emsp;approvalDate|审批日期|string(date-time)||
|&emsp;&emsp;belongCompany|所属企业|integer(int32)||
|&emsp;&emsp;belongGroup|所属集团|string||
|&emsp;&emsp;createBy|创建人|string||
|&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;effectiveDate|生效日期|string(date-time)||
|&emsp;&emsp;expiryDate|失效时间|string(date-time)||
|&emsp;&emsp;handlerUserName|经办人|string||
|&emsp;&emsp;id|ID|integer(int32)||
|&emsp;&emsp;inputTime|录入时间|string(date-time)||
|&emsp;&emsp;inputUserName|录入人|string||
|&emsp;&emsp;institutionAttachment|制度正式附件文件列表|array|上传附件信息|
|&emsp;&emsp;&emsp;&emsp;name|文件名|string||
|&emsp;&emsp;&emsp;&emsp;url|文件路径|string||
|&emsp;&emsp;institutionCode|制度编号|string||
|&emsp;&emsp;institutionName|制度名称|string||
|&emsp;&emsp;manageCompany|管理单位|integer(int32)||
|&emsp;&emsp;manageCompanyName|管理单位|string||
|&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;reported|是否报送国资委|integer(int32)||
|&emsp;&emsp;status|状态|integer(int32)||
|&emsp;&emsp;updateBy|更新人|string||
|&emsp;&emsp;updateTime|更新时间|string(date-time)||
|success|成功标志|boolean||
|timestamp|时间戳|integer(int64)|integer(int64)|


**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"result": {
		"approvalDate": "",
		"belongCompany": 0,
		"belongGroup": "",
		"createBy": "",
		"createTime": "",
		"effectiveDate": "",
		"expiryDate": "",
		"handlerUserName": "",
		"id": 0,
		"inputTime": "",
		"inputUserName": "",
		"institutionAttachment": [
			{
				"name": "",
				"url": ""
			}
		],
		"institutionCode": "",
		"institutionName": "",
		"manageCompany": 0,
		"manageCompanyName": "",
		"remark": "",
		"reported": 0,
		"status": 0,
		"updateBy": "",
		"updateTime": ""
	},
	"success": true,
	"timestamp": 0
}
```