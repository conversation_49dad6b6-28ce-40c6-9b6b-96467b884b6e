import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '资产项目（资产名称）',
    dataIndex: 'assetName',
    width: 200,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '资产编号',
    dataIndex: 'assetCode',
    width: 150,
  },
  {
    title: '资产类型',
    dataIndex: 'assetType',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'assets_type');
    },
  },
  {
    title: '管理单位',
    dataIndex: 'manageCompanyName',
    width: 220,
    ellipsis: true,
  },
  {
    title: '被占用资产名称',
    dataIndex: 'occupiedAssetName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reported',
    width: 130,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '自用起始日期',
    dataIndex: 'selfUseStartDate',
    width: 120,
  },
  {
    title: '自用结束日期',
    dataIndex: 'selfUseEndDate',
    width: 120,
  },
  {
    title: '自用天数',
    dataIndex: 'selfUseDays',
    width: 100,
    align: 'right',
  },
  {
    title: '自用面积(㎡)',
    dataIndex: 'selfUseArea',
    width: 120,
    align: 'right',
  },
  {
    title: '自用资产原值(万元)',
    dataIndex: 'selfUseOriginalValue',
    width: 140,
    align: 'right',
  },
  {
    title: '自用资产账面价值(万元)',
    dataIndex: 'selfUseBookValue',
    width: 140,
    align: 'right',
  },
  {
    title: '账面价值时点',
    dataIndex: 'bookValueDate',
    width: 120,
  },
  {
    title: '使用用途',
    dataIndex: 'usePurpose',
    width: 150,
    ellipsis: true,
  },
  {
    title: '是否有收益',
    dataIndex: 'hasIncome',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '是否制定办公用房标准',
    dataIndex: 'hasOfficeStandard',
    width: 180,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'office_standard');
    },
  },
  {
    title: '是否超标',
    dataIndex: 'isExceedStandard',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'record_status');
    },
  },
  {
    title: '经办人',
    dataIndex: 'handlerUserName',
    width: 100,
  },
  {
    title: '录入人',
    dataIndex: 'inputUserName',
    width: 100,
  },
  {
    title: '录入时间',
    dataIndex: 'inputTime',
    width: 160,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 160,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    ellipsis: true,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'assetProjectName',
    label: '资产项目（资产名称）',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产项目（资产名称）',
    },
    colProps: { span: 6 },
  },
  {
    field: 'assetCode',
    label: '资产编号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产编号',
    },
    colProps: { span: 6 },
  },
  {
    field: 'assetType',
    label: '资产类型',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择资产类型',
      dictCode: 'assets_type',
    },
    colProps: { span: 6 },
  },
  {
    field: 'usePurpose',
    label: '使用用途',
    component: 'Input',
    componentProps: {
      placeholder: '请输入使用用途',
    },
    colProps: { span: 6 },
  },
  {
    field: 'manageCompanyName',
    label: '管理单位',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请输入管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
    colProps: { span: 6 },
  },
  {
    field: 'occupiedAssetName',
    label: '被占用资产名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入被占用资产名称',
    },
    colProps: { span: 6 },
  },
  {
    field: 'reported',
    label: '是否报送国资委',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
    colProps: { span: 6 },
  },
  {
    field: 'selfUseStartDateRange',
    label: '自用起始日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
    },
    colProps: { span: 6 },
  },
  {
    field: 'selfUseEndDateRange',
    label: '自用结束日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
    },
    colProps: { span: 6 },
  },
  {
    field: 'minSelfUseDays',
    label: '自用天数最小值',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最少天数',
    },
    colProps: { span: 6 },
  },
  {
    field: 'maxSelfUseDays',
    label: '自用天数最大值',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最多天数',
    },
    colProps: { span: 6 },
  },
  {
    field: 'minSelfUseArea',
    label: '自用面积最小值(㎡)',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最小面积',
      precision: 2,
    },
    colProps: { span: 6 },
  },
  {
    field: 'maxSelfUseArea',
    label: '自用面积最大值(㎡)',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最大面积',
      precision: 2,
    },
    colProps: { span: 6 },
  },
  {
    field: 'minSelfUseBookValue',
    label: '账面价值最小值(万元)',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最小价值',
      precision: 2,
    },
    colProps: { span: 6 },
  },
  {
    field: 'maxSelfUseBookValue',
    label: '账面价值最大值(万元)',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最大价值',
      precision: 2,
    },
    colProps: { span: 6 },
  },
  {
    field: 'hasIncome',
    label: '是否有收益',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
    colProps: { span: 6 },
  },
  {
    field: 'isExceedStandard',
    label: '是否超标',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
    colProps: { span: 6 },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
    colProps: { span: 6 },
  },
  {
    field: 'handlerUserName',
    label: '经办人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
    colProps: { span: 6 },
  },
  {
    field: 'inputUserName',
    label: '录入人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入录入人',
    },
    colProps: { span: 6 },
  },
  {
    field: 'inputTimeRange',
    label: '录入时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
    },
    colProps: { span: 6 },
  },
  {
    field: 'updateTimeRange',
    label: '更新时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
    },
    colProps: { span: 6 },
  },
];
