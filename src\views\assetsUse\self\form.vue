<template>
  <div class="self-form">
    <div class="p-4">
      <a-form :model="formData" :rules="rules" ref="selfFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 基本信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              基本信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="id">
                  <template #label>
                    <span>序号</span>
                    <a-tooltip title="数据传到国资监管平台后将返回序号">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-input v-model:value="formData.id" placeholder="序号为只读项" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetType" label="资产类型">
                  <JDictSelectTag
                    v-model:value="formData.assetType"
                    :showChooseOption="false"
                    dictCode="assets_type"
                    @change="handleAssetTypeChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetId" :label-col="{ span: 8 }" label="资产项目（资产名称）">
                  <AssetsSelect
                    v-model:value="formData.assetId"
                    :type="formData.assetType"
                    :disabled="!formData.assetType"
                    :initData="formData.initData"
                    @change="handleAssetProjectChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetCode" label="资产编号">
                  <a-input v-model:value="formData.assetCode" placeholder="选择资产项目（资产名称）后自动带出" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupiedAssetName" label="被占用资产名称">
                  <a-input v-model:value="formData.occupiedAssetName" placeholder="请输入被占用资产名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="manageCompanyName" label="管理单位">
                  <a-input v-model:value="formData.manageCompanyName" placeholder="选择资产项目（资产名称）后自动带出" disabled />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="reported" label="是否报送国资委">
                  <JDictSelectTag v-model:value="formData.reported" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="handlerUserName" label="经办人">
                  <a-input v-model:value="formData.handlerUserName" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="inputUserName" label="录入人">
                  <a-input v-model:value="formData.inputUserName" placeholder="录入人" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="inputTime" label="录入时间">
                  <a-input v-model:value="formData.inputTime" placeholder="录入时间" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="status">
                  <template #label>
                    <span>状态</span>
                    <a-tooltip title="备案数据支持撤回、草稿数据和撤回数据支持作废">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option value="0" :disabled="isDraftDisabled">草稿</a-select-option>
                    <a-select-option value="1" :disabled="isFiledDisabled">备案</a-select-option>
                    <a-select-option value="2" :disabled="isRevokedDisabled">撤回</a-select-option>
                    <a-select-option value="4" :disabled="isVoidDisabled">作废</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 自用信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:user-outlined" class="title-icon" />
              自用信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="selfUseStartDate" label="自用起始日期">
                  <a-date-picker
                    v-model:value="formData.selfUseStartDate"
                    placeholder="请选择自用起始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="startDateDisabled"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="selfUseEndDate" label="自用结束日期">
                  <a-date-picker
                    v-model:value="formData.selfUseEndDate"
                    placeholder="请选择自用结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="endDateDisabled"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="selfUseDays" label="自用天数">
                  <a-input v-model:value="formData.selfUseDays" placeholder="自动计算" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="selfUseArea" label="自用面积（㎡）">
                  <a-input-number
                    v-model:value="formData.selfUseArea"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入自用面积"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="selfUseOriginalValue" :label-col="{ span: 8 }" label="自用资产原值（万元）">
                  <a-input-number
                    v-model:value="formData.selfUseOriginalValue"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入自用资产原值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="selfUseBookValue" :label-col="{ span: 10 }" label="自用资产账面价值（万元）">
                  <a-input-number
                    v-model:value="formData.selfUseBookValue"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入自用资产账面价值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="bookValueDate" label="账面价值时点">
                  <a-date-picker
                    v-model:value="formData.bookValueDate"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="usePurpose" label="使用用途">
                  <a-input v-model:value="formData.usePurpose" placeholder="请输入使用用途" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="hasIncome" label="是否有收益">
                  <JDictSelectTag v-model:value="formData.hasIncome" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="hasOfficeStandard" :label-col="{ span: 8 }" label="是否制定办公用房标准">
                  <JDictSelectTag v-model:value="formData.hasOfficeStandard" :showChooseOption="false" dictCode="office_standard" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="isExceedStandard" label="是否超标">
                  <JDictSelectTag v-model:value="formData.isExceedStandard" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item name="remark" :label-col="{ span: 2 }" label="备注">
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-footer">
          <a-button @click="handleReset">重置</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="submitLoading" style="margin-left: 12px"> 提交 </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" name="SelfInfoForm" setup>
  import { ref, onMounted, watch, computed, nextTick } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { saveOrUpdate, getDetail } from './self.api';
  import dayjs from 'dayjs';
  import { useUserStore } from '/@/store/modules/user';
  import { JDictSelectTag } from '/@/components/Form';
  import AssetsSelect from '/@/components/biz/select/assetsSelect.vue';

  // 定义表单数据接口
  interface FormData {
    id?: number;
    assetId?: number;
    initData?: any;
    assetType: number | null;
    assetName?: string;
    assetCode: string;
    occupiedAssetName: string;
    manageCompany?: number;
    manageCompanyName?: string;
    reported: string | null;
    handlerUserName: string;
    inputUserName: string;
    inputTime: string;
    status: string | null;
    selfUseStartDate: string;
    selfUseEndDate: string;
    selfUseDays: number | null;
    selfUseArea: number | null;
    selfUseOriginalValue: number | null;
    selfUseBookValue: number | null;
    bookValueDate: string;
    usePurpose: string;
    hasIncome: string | null;
    hasOfficeStandard: string | null;
    isExceedStandard: string | null;
    remark: string;
  }

  const router = useRouter();
  const route = useRoute();
  const userStore = useUserStore();
  const { createMessage, createConfirm } = useMessage();

  const submitLoading = ref(false);
  const isUpdate = ref(false);
  const recordId = ref<number | undefined>(undefined);
  const originalStatus = ref<string | null>(null);
  const selfFormRef = ref();

  // 表单数据
  const formData = ref<FormData>({
    id: undefined,
    assetId: undefined,
    assetType: null,
    assetName: '',
    assetCode: '',
    occupiedAssetName: '',
    manageCompany: undefined,
    manageCompanyName: '',
    reported: '',
    handlerUserName: '',
    inputUserName: userStore.getUserInfo.realname || '当前用户',
    inputTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    status: '',
    selfUseStartDate: '',
    selfUseEndDate: '',
    selfUseDays: null,
    selfUseArea: null,
    selfUseOriginalValue: null,
    selfUseBookValue: null,
    bookValueDate: '',
    usePurpose: '',
    hasIncome: '',
    hasOfficeStandard: '',
    isExceedStandard: '',
    remark: '',
  });

  // 表单验证规则
  const rules = {
    // 基本信息验证规则
    assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
    assetCode: [{ required: true, message: '请选择资产项目（资产名称）后自动带出资产编号', trigger: 'change' }],
    reported: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
    handlerUserName: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
    inputUserName: [{ required: true, message: '录入人为必填项' }],
    inputTime: [{ required: true, message: '录入时间为必填项' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],

    // 自用信息验证规则
    selfUseStartDate: [{ required: true, message: '请选择自用起始日期', trigger: 'change' }],
    selfUseEndDate: [
      {
        validator: (rule: any, value: string) => {
          if (!value) return Promise.resolve();
          if (!formData.value.selfUseStartDate) {
            return Promise.reject(new Error('请先选择自用起始日期'));
          }
          const startDate = new Date(formData.value.selfUseStartDate);
          const endDate = new Date(value);
          if (endDate <= startDate) {
            return Promise.reject(new Error('自用结束日期必须大于自用起始日期'));
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
    selfUseArea: [
      {
        validator: (rule: any, value: number) => {
          if ((formData.value.assetType === 0 || formData.value.assetType === 1) && (value === null || value === undefined || value === 0)) {
            return Promise.reject(new Error('请输入自用面积'));
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
    selfUseBookValue: [{ required: true, message: '请输入自用资产账面价值', trigger: 'blur' }],
    bookValueDate: [{ required: true, message: '请选择账面价值时点', trigger: 'change' }],
    usePurpose: [{ required: true, message: '请输入使用用途', trigger: 'blur' }],
  };

  // 计算属性：状态控制
  const isDraftDisabled = computed(() => isUpdate.value);
  const isFiledDisabled = computed(() => {
    if (!isUpdate.value) return false;
    return originalStatus.value !== '0';
  });
  const isRevokedDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value !== '1';
  });
  const isVoidDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value === null || !['0', '2'].includes(originalStatus.value);
  });

  // 监听资产类型变化，更新自用面积必填状态
  watch(
    () => formData.value.assetType,
    () => {
      // 触发自用面积字段的重新验证
      nextTick(() => {
        selfFormRef.value?.validateFields(['selfUseArea']);
      });
    }
  );

  // 监听日期变化，自动计算自用天数
  watch(
    [() => formData.value.selfUseStartDate, () => formData.value.selfUseEndDate],
    ([startDate, endDate]) => {
      calculateSelfDays(startDate, endDate);
    }
  );

  // 计算自用天数
  function calculateSelfDays(startDate: string, endDate: string) {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      formData.value.selfUseDays = diffDays;
    } else if (startDate) {
      const start = new Date(startDate);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      formData.value.selfUseDays = diffDays;
    } else {
      formData.value.selfUseDays = null;
    }
  }

  // 验证表单
  async function validate() {
    try {
      await selfFormRef.value?.validate();
      return true;
    } catch (error) {
      return false;
    }
  }

  // 获取表单数据
  function getFormData() {
    return { ...formData.value };
  }

  // 提交表单
  async function handleSubmit() {
    try {
      const isValid = await validate();
      if (!isValid) {
        return;
      }

      submitLoading.value = true;

      const submitData = getFormData();

      // 如果是编辑模式，添加ID
      if (isUpdate.value) {
        submitData.id = recordId.value;
      }

      await saveOrUpdate(submitData);
      createMessage.success(isUpdate.value ? '更新成功' : '新增成功');

      // 返回列表页
      router.push('/assetsUse/self');
    } catch (error) {
      createMessage.error('操作失败');
    } finally {
      submitLoading.value = false;
    }
  }

  // 重置表单
  function handleReset() {
    createConfirm({
      iconType: 'warning',
      title: '确认重置',
      content: '确定要重置表单吗？所有已填写的数据将会清空',
      onOk: () => {
        resetFields();
        if (!isUpdate.value) {
          setDefaultValues();
        }
        createMessage.success('表单已重置');
      },
    });
  }

  // 重置所有表单
  function resetFields() {
    selfFormRef.value?.resetFields();
    formData.value = {
      id: undefined,
      assetId: undefined,
      assetType: null,
      assetName: '',
      assetCode: '',
      occupiedAssetName: '',
      manageCompany: undefined,
      manageCompanyName: '',
      reported: '',
      handlerUserName: '',
      inputUserName: userStore.getUserInfo.realname || '当前用户',
      inputTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      status: '',
      selfUseStartDate: '',
      selfUseEndDate: '',
      selfUseDays: null,
      selfUseArea: null,
      selfUseOriginalValue: null,
      selfUseBookValue: null,
      bookValueDate: '',
      usePurpose: '',
      hasIncome: '',
      hasOfficeStandard: '',
      isExceedStandard: '',
      remark: '',
    };
  }

  // 设置默认值
  function setDefaultValues() {
    formData.value.status = ''; // 默认草稿状态
    formData.value.inputUserName = userStore.getUserInfo.realname || '当前用户'; // 录入人
    formData.value.inputTime = dayjs().format('YYYY-MM-DD HH:mm:ss'); // 录入时间
    formData.value.reported = ''; // 默认否
    formData.value.hasIncome = ''; // 默认否
    formData.value.hasOfficeStandard = ''; // 默认否
    formData.value.isExceedStandard = ''; // 默认否
  }

  // 设置表单值
  function setFieldsValue(data: any) {
    formData.value = {
      id: data.id,
      assetId: data.assetId,
      initData: {
        label: `${data.assetName}(${data.assetCode})`,
        name: data.assetName,
        value: data.assetId,
        code: data.assetCode,
        manageUnitName: data.manageCompanyName,
      },
      assetType: `${data.assetType}`,
      assetName: data.assetName || '',
      assetCode: data.assetCode || '',
      occupiedAssetName: data.occupiedAssetName || '',
      manageCompany: data.manageCompany,
      manageCompanyName: data.manageCompanyName || '',
      reported: `${data.reported}`,
      handlerUserName: data.handlerUserName || '',
      inputUserName: data.inputUserName || '',
      inputTime: data.inputTime || '',
      status: `${data.status}`,
      selfUseStartDate: data.selfUseStartDate || '',
      selfUseEndDate: data.selfUseEndDate || '',
      selfUseDays: data.selfUseDays,
      selfUseArea: data.selfUseArea,
      selfUseOriginalValue: data.selfUseOriginalValue,
      selfUseBookValue: data.selfUseBookValue,
      bookValueDate: data.bookValueDate || '',
      usePurpose: data.usePurpose || '',
      hasIncome: `${data.hasIncome}`,
      hasOfficeStandard: `${data.hasOfficeStandard}`,
      isExceedStandard: `${data.isExceedStandard}`,
      remark: data.remark || '',
    };
  }

  // 处理资产类型变更
  function handleAssetTypeChange() {
    formData.value.assetId = '';
    formData.value.assetName = '';
    formData.value.assetCode = '';
    formData.value.occupiedAssetName = '';
    formData.value.manageCompany = undefined;
    formData.value.manageCompanyName = '';
  }

  // 处理资产项目变更
  function handleAssetProjectChange(value: string, option: any) {
    formData.value.assetCode = option.code || '';
    formData.value.manageCompanyName = option.manageUnitName || '';
    formData.value.occupiedAssetName = option.name || '';
  }

  // 日期禁用函数
  function startDateDisabled(current: any) {
    // 使用dayjs判断日期是否大于当前日期
    if (current && dayjs(current).isAfter(dayjs(), 'day')) {
      return true;
    }

    // 如果已经选择了结束日期，起始日期不能晚于结束日期
    if (formData.value.selfUseEndDate) {
      return dayjs(current).isAfter(dayjs(formData.value.selfUseEndDate), 'day');
    }

    return false;
  }

  function endDateDisabled(current: any) {
    if (!formData.value.selfUseStartDate) return false;
    const startDate = dayjs(formData.value.selfUseStartDate).valueOf();
    return startDate >= current.valueOf();
  }

  // 加载详情数据
  async function loadDetail() {
    const id = route.query.id as string;
    if (id) {
      isUpdate.value = true;
      recordId.value = parseInt(id);
      try {
        const result = await getDetail(id);
        const data = result.data || result;
        setFieldsValue(data);
        originalStatus.value = data.status;
      } catch (error) {
        createMessage.error('加载数据失败');
      }
    } else {
      isUpdate.value = false;
      setDefaultValues();
    }
  }

  onMounted(() => {
    loadDetail();
  });
</script>

<style lang="less" scoped>
  .self-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    // 帮助文本样式
    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    // 提示图标样式
    .tooltip-icon {
      color: #909399;
      margin-left: 5px;
      cursor: pointer;
    }
  }
</style>