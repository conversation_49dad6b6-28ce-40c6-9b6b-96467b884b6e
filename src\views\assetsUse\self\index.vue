<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">新增</a-button>
        <import-modal @success="importSuccess" exportTemplateName="自用信息导入模板" :exportTemplateUrl="downloadTemplate" :importUrl="importExcel" />
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="handleExport" :disabled="selectedRowKeys.length === 0">导出</a-button>
        <a-button type="primary" preIcon="ant-design:download-outlined" @click="handleExportAll">全部导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup name="SelfInfoList">
  import { ref } from 'vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { columns, searchFormSchema } from './self.data';
  import { list, deleteSelf, exportExcel, importExcel, downloadTemplate } from './self.api';
  import { mapTableTotalSummary } from '/@/utils/common/compUtils';
  import { useMethods } from '/@/hooks/system/useMethods';
  import ImportModal from '/@/components/ImportModal/index.vue';

  const { createMessage } = useMessage();
  const router = useRouter();
  const exportLoading = ref(false);
  const { handleExportXls } = useMethods();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'self-info-list',
    tableProps: {
      title: '自用信息列表',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      showIndexColumn: true,
      showTableSetting: true,
      formConfig: {
        labelWidth: 140,
        schemas: searchFormSchema,
        fieldMapToTime: [
          ['selfUseStartDateRange', ['minSelfUseStartDate', 'maxSelfUseStartDate'], 'YYYY-MM-DD'],
          ['selfUseEndDateRange', ['minSelfUseEndDate', 'maxSelfUseEndDate'], 'YYYY-MM-DD'],
          ['inputTimeRange', ['minInputTime', 'maxInputTime'], 'YYYY-MM-DD'],
          ['updateTimeRange', ['minUpdateTime', 'maxUpdateTime'], 'YYYY-MM-DD'],
        ],
      },
      actionColumn: {
        width: 160,
        fixed: 'right',
      },
      showSummary: true,
      summaryFunc: (data) => {
        const totals = mapTableTotalSummary(data, ['selfUseDays', 'selfUseArea', 'selfUseOriginalValue', 'selfUseBookValue']);
        // 格式化数值
        Object.keys(totals).forEach((key) => {
          if (typeof totals[key] === 'number') {
            totals[key] = Number(totals[key].toFixed(2));
          }
        });
        return [totals];
      },
      tableSetting: {
        redo: false,
      },
    },
  });

  // 获取表格上下文
  const [registerTable, { reload, getForm }] = tableContext;

  // 选中的行key
  const selectedRowKeys = ref<string[]>([]);

  // 获取选中的行
  const rowSelection = {
    type: 'checkbox',
    onChange: (keys: string[]) => {
      selectedRowKeys.value = keys;
    },
  };

  // 新增按钮点击事件
  function handleCreate() {
    router.push({
      path: '/assetsUse/self/add',
      query: {
        type: 'add',
      },
    });
  }

  // 编辑按钮点击事件
  function handleEdit(record: any) {
    router.push({
      path: '/assetsUse/self/edit/' + record.id,
      query: {
        id: record.id,
        type: 'edit',
      },
    });
  }

  function importSuccess() {
    reload();
  }

  // 删除按钮点击事件
  async function handleDelete(record: any) {
    try {
      await deleteSelf({ id: record.id });
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  /**
   * 导出选中数据
   */
  async function handleExport() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }
    const formData = getForm().getFieldsValue();
    formData.ids = selectedRowKeys.value;
    exportLoading.value = true;
    await handleExportXls('占用信息列表', exportExcel, formData, 'post').then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导出全部数据
   */
  async function handleExportAll() {
    const formData = getForm().getFieldsValue();
    console.log(formData, 'formData');
    exportLoading.value = true;
    await handleExportXls('占用信息列表', exportExcel, formData, 'post').then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  // 获取表格操作列配置
  function getTableAction(record: any): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];

    // 根据状态决定是否显示删除按钮
    if (record.status === 0 || record.status === 2) {
      // 草稿或撤回状态可以删除
      actions.push({
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除吗？',
          confirm: handleDelete.bind(null, record),
        },
      });
    }

    return actions;
  }
</script>

<style lang="less" scoped>
@import url('./index.less');
</style> 