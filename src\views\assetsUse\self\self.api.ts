import { defHttp } from '/@/utils/http/axios';

enum Api {
  SelfList = '/biz/selfuse/page',
  SelfSave = '/biz/selfuse/save',
  SelfDelete = '/biz/selfuse/delete',
  SelfDetail = '/biz/selfuse/detail',
  SelfExport = '/mock/self/export',
  SelfExportAll = '/biz/selfuse/excel/export',
  SelfImport = '/biz/selfuse/excel/import',
  SelfTemplate = '/biz/selfuse/template/excel/download',
}

export const exportExcel = Api.SelfExport;
export const downloadTemplate = Api.SelfTemplate;
export const importExcel = Api.SelfImport;

/**
 * 获取自用信息列表
 */
export const list = (params: any) => {
  return defHttp.post({ url: Api.SelfList, params });
};

/**
 * 保存或更新自用信息
 */
export const saveOrUpdate = (params: any) => {
  // 根据文档，新增和编辑都使用同一个接口
  return defHttp.post({ url: Api.SelfSave, params });
};

/**
 * 删除自用信息
 */
export const deleteSelf = (params: any) => {
  return defHttp.get({ url: Api.SelfDelete, params });
};

/**
 * 获取自用信息详情
 */
export const getDetail = (id: string) => {
  return defHttp.get({ url: Api.SelfDetail, params: { id } });
};
