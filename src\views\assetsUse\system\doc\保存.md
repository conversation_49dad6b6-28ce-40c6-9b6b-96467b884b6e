

## 制度信息-添加


**接口地址**:`/jeecg-boot/biz/institution/save`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "approvalDate": "",
  "belongCompany": 0,
  "belongGroup": 0,
  "effectiveDate": "",
  "expiryDate": "",
  "handlerUserName": "",
  "inputTime": "",
  "inputUserName": "",
  "institutionAttachment": [
    {
      "name": "",
      "url": ""
    }
  ],
  "institutionCode": "",
  "institutionName": "",
  "manageCompany": 0,
  "remark": "",
  "reported": 0,
  "status": 0
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|dto|dto|body|true|资产制度信息-保存提交|资产制度信息-保存提交|
|&emsp;&emsp;approvalDate|审批日期||false|string(date-time)||
|&emsp;&emsp;belongCompany|所属企业||false|integer(int32)||
|&emsp;&emsp;belongGroup|所属集团||false|integer(int32)||
|&emsp;&emsp;effectiveDate|生效日期||false|string(date-time)||
|&emsp;&emsp;expiryDate|失效时间||false|string(date-time)||
|&emsp;&emsp;handlerUserName|经办人||false|string||
|&emsp;&emsp;inputTime|录入时间||false|string(date-time)||
|&emsp;&emsp;inputUserName|录入人||false|string||
|&emsp;&emsp;institutionAttachment|制度正式附件文件列表||false|array|上传附件信息|
|&emsp;&emsp;&emsp;&emsp;name|文件名||false|string||
|&emsp;&emsp;&emsp;&emsp;url|文件路径||false|string||
|&emsp;&emsp;institutionCode|制度编号||false|string||
|&emsp;&emsp;institutionName|制度名称||false|string||
|&emsp;&emsp;manageCompany|管理单位||false|integer(int32)||
|&emsp;&emsp;remark|备注||false|string||
|&emsp;&emsp;reported|是否报送国资委||false|integer(int32)||
|&emsp;&emsp;status|状态||false|integer(int32)||
|X-Access-Token|token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|接口返回对象«string»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回代码|integer(int32)|integer(int32)|
|message|返回处理消息|string||
|result|返回数据对象|string||
|success|成功标志|boolean||
|timestamp|时间戳|integer(int64)|integer(int64)|


**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"result": "",
	"success": true,
	"timestamp": 0
}
```