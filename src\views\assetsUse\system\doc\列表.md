

## 制度信息-分页列表查询


**接口地址**:`/jeecg-boot/biz/institution/page`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "belongCompany": 0,
  "handlerUserName": "",
  "inputUserName": "",
  "institutionCode": "",
  "institutionName": "",
  "manageCompany": 0,
  "maxApprovalDate": "",
  "maxEffectiveDate": "",
  "maxExpiryDate": "",
  "maxInputTime": "",
  "maxUpdateTime": "",
  "minApprovalDate": "",
  "minEffectiveDate": "",
  "minExpiryDate": "",
  "minInputTime": "",
  "minUpdateTime": "",
  "pageNo": 0,
  "pageSize": 0,
  "reported": 0,
  "status": 0
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|queryRo|queryRo|body|true|制度信息检索|制度信息检索|
|&emsp;&emsp;belongCompany|所属企业||false|integer(int32)||
|&emsp;&emsp;handlerUserName|经办人||false|string||
|&emsp;&emsp;inputUserName|录入人||false|string||
|&emsp;&emsp;institutionCode|制度编号||false|string||
|&emsp;&emsp;institutionName|制度名称||false|string||
|&emsp;&emsp;manageCompany|管理单位||false|integer(int32)||
|&emsp;&emsp;maxApprovalDate|审批日期-最大||false|string(date-time)||
|&emsp;&emsp;maxEffectiveDate|生效日期-最大||false|string(date-time)||
|&emsp;&emsp;maxExpiryDate|失效时间-最大||false|string(date-time)||
|&emsp;&emsp;maxInputTime|录入时间-最大||false|string(date-time)||
|&emsp;&emsp;maxUpdateTime|更新时间-最大||false|string(date-time)||
|&emsp;&emsp;minApprovalDate|审批日期-最小||false|string(date-time)||
|&emsp;&emsp;minEffectiveDate|生效日期-最小||false|string(date-time)||
|&emsp;&emsp;minExpiryDate|失效时间-最小||false|string(date-time)||
|&emsp;&emsp;minInputTime|录入时间-最小||false|string(date-time)||
|&emsp;&emsp;minUpdateTime|更新时间-最小||false|string(date-time)||
|&emsp;&emsp;pageNo|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;reported|是否报送国资委||false|integer(int32)||
|&emsp;&emsp;status|状态||false|integer(int32)||
|X-Access-Token|token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|接口返回对象«IPage«资产制度信息»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回代码|integer(int32)|integer(int32)|
|message|返回处理消息|string||
|result|返回数据对象|IPage«资产制度信息»|IPage«资产制度信息»|
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|&emsp;&emsp;records||array|资产制度信息|
|&emsp;&emsp;&emsp;&emsp;approvalDate|审批日期|string||
|&emsp;&emsp;&emsp;&emsp;belongCompany|所属企业|integer||
|&emsp;&emsp;&emsp;&emsp;belongGroup|所属集团|string||
|&emsp;&emsp;&emsp;&emsp;createBy|创建人|string||
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间|string||
|&emsp;&emsp;&emsp;&emsp;effectiveDate|生效日期|string||
|&emsp;&emsp;&emsp;&emsp;expiryDate|失效时间|string||
|&emsp;&emsp;&emsp;&emsp;handlerUserName|经办人|string||
|&emsp;&emsp;&emsp;&emsp;id|ID|integer||
|&emsp;&emsp;&emsp;&emsp;inputTime|录入时间|string||
|&emsp;&emsp;&emsp;&emsp;inputUserName|录入人|string||
|&emsp;&emsp;&emsp;&emsp;institutionAttachment|制度正式附件文件列表|array|上传附件信息|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|文件名|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;url|文件路径|string||
|&emsp;&emsp;&emsp;&emsp;institutionCode|制度编号|string||
|&emsp;&emsp;&emsp;&emsp;institutionName|制度名称|string||
|&emsp;&emsp;&emsp;&emsp;manageCompany|管理单位|integer||
|&emsp;&emsp;&emsp;&emsp;manageCompanyName|管理单位|string||
|&emsp;&emsp;&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;&emsp;&emsp;reported|是否报送国资委|integer||
|&emsp;&emsp;&emsp;&emsp;status|状态|integer||
|&emsp;&emsp;&emsp;&emsp;updateBy|更新人|string||
|&emsp;&emsp;&emsp;&emsp;updateTime|更新时间|string||
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|success|成功标志|boolean||
|timestamp|时间戳|integer(int64)|integer(int64)|


**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"result": {
		"current": 0,
		"pages": 0,
		"records": [
			{
				"approvalDate": "",
				"belongCompany": 0,
				"belongGroup": "",
				"createBy": "",
				"createTime": "",
				"effectiveDate": "",
				"expiryDate": "",
				"handlerUserName": "",
				"id": 0,
				"inputTime": "",
				"inputUserName": "",
				"institutionAttachment": [
					{
						"name": "",
						"url": ""
					}
				],
				"institutionCode": "",
				"institutionName": "",
				"manageCompany": 0,
				"manageCompanyName": "",
				"remark": "",
				"reported": 0,
				"status": 0,
				"updateBy": "",
				"updateTime": ""
			}
		],
		"size": 0,
		"total": 0
	},
	"success": true,
	"timestamp": 0
}
```